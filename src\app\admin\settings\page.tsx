'use client'

import { useState, useEffect } from 'react'
import Image from 'next/image'
import {
  Settings as SettingsIcon,
  User,
  Bell,
  Shield,
  Database,
  Save,
  RefreshCw,
  Eye,
  EyeOff,
  Image as ImageIcon
} from 'lucide-react'
import { useSiteSettings } from '@/lib/redux/hooks'
import { uploadSiteSettingFile } from '@/lib/redux/slices/siteSettingsSlice'

interface SettingSection {
  id: string
  title: string
  description: string
  icon: React.ReactNode
}

const settingSections: SettingSection[] = [
  {
    id: 'site',
    title: 'Site Settings',
    description: 'Manage site branding, logos, and visual assets',
    icon: <ImageIcon className="h-5 w-5" />
  },
  {
    id: 'general',
    title: 'General Settings',
    description: 'Basic system configuration and preferences',
    icon: <SettingsIcon className="h-5 w-5" />
  },
  {
    id: 'profile',
    title: 'Profile Settings',
    description: 'Manage your personal information and preferences',
    icon: <User className="h-5 w-5" />
  },
  {
    id: 'notifications',
    title: 'Notifications',
    description: 'Configure notification preferences and alerts',
    icon: <Bell className="h-5 w-5" />
  },
  {
    id: 'security',
    title: 'Security',
    description: 'Password, authentication, and security settings',
    icon: <Shield className="h-5 w-5" />
  },
  {
    id: 'system',
    title: 'System Settings',
    description: 'Database, backup, and system configuration',
    icon: <Database className="h-5 w-5" />
  }
]

export default function SettingsPage() {
  const [activeSection, setActiveSection] = useState('site')
  const [showPassword, setShowPassword] = useState(false)
  // const [showMediaLibrary, setShowMediaLibrary] = useState(false)
  const [, setCurrentUploadType] = useState<string | null>(null)

  // Site settings Redux hook
  const {
    settings: siteSettings,
    loading: siteSettingsLoading,
    error: siteSettingsError,
    // updateSetting,
    uploadFile,
    getSettingUrl,
    isUploading,
    clearError
  } = useSiteSettings()

  // Debug authentication state
  useEffect(() => {
    console.log('[Settings Page] Site settings loading:', siteSettingsLoading)
    console.log('[Settings Page] Site settings:', siteSettings)
    console.log('[Settings Page] Site settings error:', siteSettingsError)
  }, [siteSettingsLoading, siteSettings, siteSettingsError])

  // Local state for non-site settings (general, profile, notifications, security, system)
  const [settings, setSettings] = useState({
    // General Settings
    systemName: 'Municipal Agriculture Office - Ipil',
    timezone: 'Asia/Manila',
    language: 'English',
    dateFormat: 'MM/DD/YYYY',

    // Profile Settings
    firstName: 'Admin',
    lastName: 'User',
    email: '<EMAIL>',
    phone: '+63 ************',

    // Notification Settings
    emailNotifications: true,
    smsNotifications: false,
    pushNotifications: true,
    weeklyReports: true,

    // Security Settings
    twoFactorAuth: false,
    sessionTimeout: '30',
    passwordExpiry: '90',

    // System Settings
    autoBackup: true,
    backupFrequency: 'daily',
    maintenanceMode: false,
    debugMode: false
  })

  const handleSettingChange = (key: string, value: string | boolean) => {
    setSettings(prev => ({
      ...prev,
      [key]: value
    }))
  }

  const handleFileUpload = async (file: File, type: 'logo' | 'background' | 'favicon') => {
    try {
      // Clear any previous errors
      clearError()

      // Basic validation - very lenient for debugging
      if (!file) {
        alert('Please select a file to upload.')
        return
      }

      // Very lenient file type check - accept most common image formats and even some that might not be detected properly
      const fileName = file.name.toLowerCase()
      const validExtensions = ['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp', '.svg', '.tiff', '.tif']
      const hasValidExtension = validExtensions.some(ext => fileName.endsWith(ext))

      if (!file.type.startsWith('image/') && !hasValidExtension) {
        console.warn('[Settings Page] File type check:', { fileType: file.type, fileName: file.name })
        alert('Please select an image file. Supported formats: JPG, PNG, GIF, BMP, WebP, SVG, TIFF')
        return
      }

      // Very generous file size limit (100MB) for debugging
      const maxSize = 100 * 1024 * 1024 // 100MB
      if (file.size > maxSize) {
        alert(`File size is ${(file.size / 1024 / 1024).toFixed(2)}MB. Maximum allowed is 100MB.`)
        return
      }

      // Map upload type to setting key
      let settingKey = ''
      if (type === 'logo') {
        settingKey = 'site_logo'
      } else if (type === 'background') {
        settingKey = 'hero_background'
      } else if (type === 'favicon') {
        settingKey = 'site_favicon'
      }

      console.log(`[Settings Page] Uploading ${type} file:`, {
        fileName: file.name,
        fileSize: `${(file.size / 1024 / 1024).toFixed(2)}MB`,
        fileType: file.type,
        settingKey,
        lastModified: new Date(file.lastModified).toISOString(),
        acceptedForDebugging: 'YES - Very lenient validation for debugging'
      })

      // Use Redux upload action
      const result = await uploadFile(settingKey, file, type)

      // Check if upload was successful using the correct action creator
      if (uploadSiteSettingFile.fulfilled.match(result)) {
        console.log(`[Settings Page] ${type} uploaded successfully:`, result.payload)

        // Show success message and refresh for hero background
        if (type === 'background') {
          const fileInfo = `File: ${file.name} (${(file.size / 1024 / 1024).toFixed(2)}MB)`
          alert(`🎉 Hero background uploaded successfully!\n${fileInfo}\nThe page will refresh to show your changes.`)
          setTimeout(() => {
            window.location.reload()
          }, 1500) // Give a bit more time to read the message
        } else {
          const fileInfo = `File: ${file.name} (${(file.size / 1024 / 1024).toFixed(2)}MB)`
          alert(`🎉 ${type.charAt(0).toUpperCase() + type.slice(1)} uploaded successfully!\n${fileInfo}`)
        }
      } else if (uploadSiteSettingFile.rejected.match(result)) {
        // Handle specific error cases
        const errorMessage = result.payload as string
        console.error(`[Settings Page] Upload failed:`, errorMessage)

        let userFriendlyMessage = `Failed to upload ${type}.`

        if (errorMessage?.includes('Authentication')) {
          userFriendlyMessage += ' Please log in again and try.'
        } else if (errorMessage?.includes('Missing required fields')) {
          userFriendlyMessage += ' Invalid file data. Please try selecting the file again.'
        } else if (errorMessage?.includes('Cloudinary')) {
          userFriendlyMessage += ' Image processing failed. Please try a different image format.'
        } else if (errorMessage?.includes('Database')) {
          userFriendlyMessage += ' Database error. The image was uploaded but settings may not be saved.'
        } else if (errorMessage?.includes('does not exist')) {
          userFriendlyMessage += ' Database configuration issue. Please contact your administrator.'
        } else if (errorMessage) {
          userFriendlyMessage += ` Error: ${errorMessage}`
        } else {
          userFriendlyMessage += ' Please check your internet connection and try again.'
        }

        alert(`❌ ${userFriendlyMessage}`)
      } else {
        console.error(`[Settings Page] Unexpected result:`, result)
        alert(`❌ Failed to upload ${type}. Unexpected response from server.`)
      }

    } catch (error) {
      console.error(`[Settings Page] Error uploading ${type}:`, error)

      let errorMessage = `Failed to upload ${type}.`

      if (error instanceof Error) {
        if (error.message.includes('fetch')) {
          errorMessage += ' Network error. Please check your internet connection.'
        } else if (error.message.includes('timeout')) {
          errorMessage += ' Upload timed out. Please try again with a smaller file.'
        } else {
          errorMessage += ` Error: ${error.message}`
        }
      } else {
        errorMessage += ' Please try again.'
      }

      alert(`❌ ${errorMessage}`)
    }
  }

  // const handleMediaLibrarySelect = async (mediaItem: { secure_url: string; public_id: string }) => {
  //   if (!currentUploadType) return

  //   try {
  //     // Save to database using site settings
  //     let settingKey = ''
  //     if (currentUploadType === 'logo') {
  //       settingKey = 'site_logo'
  //     } else if (currentUploadType === 'background') {
  //       settingKey = 'hero_background'
  //     } else if (currentUploadType === 'favicon') {
  //       settingKey = 'site_favicon'
  //     }

  //     const success = await updateSetting(
  //       settingKey,
  //       mediaItem.secure_url,
  //       mediaItem.secure_url,
  //       mediaItem.public_id
  //     )

  //     if (success) {
  //       // Update local settings state for immediate UI feedback
  //       const updates: Record<string, string> = {}
  //       if (currentUploadType === 'logo') {
  //         updates.siteLogo = mediaItem.secure_url
  //         updates.siteLogoCloudinaryId = mediaItem.public_id
  //       } else if (currentUploadType === 'background') {
  //         updates.heroBackground = mediaItem.secure_url
  //         updates.heroBackgroundCloudinaryId = mediaItem.public_id
  //       } else if (currentUploadType === 'favicon') {
  //         updates.siteFavicon = mediaItem.secure_url
  //         updates.siteFaviconCloudinaryId = mediaItem.public_id
  //       }

  //       setSettings(prev => ({ ...prev, ...updates }))
  //     } else {
  //       alert('❌ Failed to save setting. Please check your connection and try again.')
  //     }
  //   } catch (error) {
  //     console.error('Error selecting from media library:', error)

  //     let errorMessage = 'Failed to save setting from media library.'
  //     if (error instanceof Error) {
  //       errorMessage += ` Error: ${error.message}`
  //     } else {
  //       errorMessage += ' Please try again.'
  //     }

  //     alert(`❌ ${errorMessage}`)
  //   }

  //   setShowMediaLibrary(false)
  //   setCurrentUploadType(null)
  // }

  const openMediaLibrary = (type: 'logo' | 'background' | 'favicon') => {
    setCurrentUploadType(type)
    // setShowMediaLibrary(true)
  }

  const renderSiteSettings = () => (
    <div className="space-y-8">
      {/* Site Logo */}
      <div className="bg-gray-50 rounded-lg p-6">
        <h3 className="text-lg font-medium text-gray-900 mb-4">Site Logo</h3>
        <p className="text-sm text-gray-600 mb-4">
          This logo will appear in the header, sidebar, and throughout the application.
        </p>

        <div className="flex items-start space-x-6">
          {/* Current Logo Preview */}
          <div className="flex-shrink-0">
            <div className="w-24 h-24 border-2 border-gray-300 rounded-lg flex items-center justify-center bg-white">
              {getSettingUrl('site_logo', '/images/logo.png') ? (
                <Image
                  src={getSettingUrl('site_logo', '/images/logo.png') || ''}
                  alt="Current Logo"
                  width={96}
                  height={96}
                  className="max-w-full max-h-full object-contain"
                />
              ) : (
                <ImageIcon className="w-8 h-8 text-gray-400" />
              )}
            </div>
            <p className="text-xs text-gray-500 mt-2 text-center">Current Logo</p>
          </div>

          {/* Upload Controls */}
          <div className="flex-1">
            <div className="space-y-3">
              <div>
                <input
                  type="file"
                  id="logo-upload"
                  accept="image/*"
                  onChange={(e) => {
                    const file = e.target.files?.[0]
                    if (file) handleFileUpload(file, 'logo')
                  }}
                  className="hidden"
                />
                <label
                  htmlFor="logo-upload"
                  className={`inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 cursor-pointer ${
                    isUploading('site_logo') ? 'opacity-50 cursor-not-allowed' : ''
                  }`}
                >
                  {isUploading('site_logo') ? 'Uploading...' : 'Upload New Logo'}
                </label>
              </div>

              <div>
                <button
                  type="button"
                  onClick={() => openMediaLibrary('logo')}
                  className="inline-flex items-center px-4 py-2 border border-blue-300 rounded-md shadow-sm text-sm font-medium text-blue-700 bg-blue-50 hover:bg-blue-100"
                >
                  Choose from Media Library
                </button>
              </div>
            </div>

            <div className="mt-3 text-xs text-gray-500">
              <p>Recommended: PNG or SVG format, 200x200px or larger</p>
              <p>Maximum file size: 5MB</p>
            </div>
          </div>
        </div>
      </div>

      {/* Hero Background */}
      <div className="bg-gray-50 rounded-lg p-6">
        <h3 className="text-lg font-medium text-gray-900 mb-4">Hero Background</h3>
        <p className="text-sm text-gray-600 mb-4">
          Background image for the homepage hero section.
        </p>

        <div className="flex items-start space-x-6">
          {/* Current Background Preview */}
          <div className="flex-shrink-0">
            <div className="w-32 h-20 border-2 border-gray-300 rounded-lg flex items-center justify-center bg-white overflow-hidden">
              {getSettingUrl('hero_background', '/images/lgu-ipil.png') ? (
                <Image
                  src={getSettingUrl('hero_background', '/images/lgu-ipil.png') || ''}
                  alt="Current Background"
                  width={128}
                  height={80}
                  className="w-full h-full object-cover"
                />
              ) : (
                <ImageIcon className="w-8 h-8 text-gray-400" />
              )}
            </div>
            <p className="text-xs text-gray-500 mt-2 text-center">Current Background</p>
          </div>

          {/* Upload Controls */}
          <div className="flex-1">
            <div className="space-y-3">
              <div>
                <input
                  type="file"
                  id="background-upload"
                  accept="image/*"
                  onChange={(e) => {
                    const file = e.target.files?.[0]
                    if (file) handleFileUpload(file, 'background')
                  }}
                  className="hidden"
                />
                <label
                  htmlFor="background-upload"
                  className={`inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 cursor-pointer ${
                    isUploading('hero_background') ? 'opacity-50 cursor-not-allowed' : ''
                  }`}
                >
                  {isUploading('hero_background') ? 'Uploading...' : 'Upload New Background'}
                </label>
              </div>

              <div>
                <button
                  type="button"
                  onClick={() => openMediaLibrary('background')}
                  className="inline-flex items-center px-4 py-2 border border-blue-300 rounded-md shadow-sm text-sm font-medium text-blue-700 bg-blue-50 hover:bg-blue-100"
                >
                  Choose from Media Library
                </button>
              </div>
            </div>

            <div className="mt-3 text-xs text-gray-500">
              <p>Recommended: JPG or PNG format, 1920x1080px or larger</p>
              <p>Maximum file size: 10MB</p>
            </div>
          </div>
        </div>
      </div>

      {/* Favicon */}
      <div className="bg-gray-50 rounded-lg p-6">
        <h3 className="text-lg font-medium text-gray-900 mb-4">Favicon</h3>
        <p className="text-sm text-gray-600 mb-4">
          Small icon that appears in browser tabs and bookmarks.
        </p>

        <div className="flex items-start space-x-6">
          {/* Current Favicon Preview */}
          <div className="flex-shrink-0">
            <div className="w-16 h-16 border-2 border-gray-300 rounded-lg flex items-center justify-center bg-white">
              {getSettingUrl('site_favicon') ? (
                <Image
                  src={getSettingUrl('site_favicon') || ''}
                  alt="Current Favicon"
                  width={64}
                  height={64}
                  className="max-w-full max-h-full object-contain"
                />
              ) : (
                <ImageIcon className="w-6 h-6 text-gray-400" />
              )}
            </div>
            <p className="text-xs text-gray-500 mt-2 text-center">Current Favicon</p>
          </div>

          {/* Upload Controls */}
          <div className="flex-1">
            <div className="space-y-3">
              <div>
                <input
                  type="file"
                  id="favicon-upload"
                  accept="image/*"
                  onChange={(e) => {
                    const file = e.target.files?.[0]
                    if (file) handleFileUpload(file, 'favicon')
                  }}
                  className="hidden"
                />
                <label
                  htmlFor="favicon-upload"
                  className={`inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 cursor-pointer ${
                    isUploading('site_favicon') ? 'opacity-50 cursor-not-allowed' : ''
                  }`}
                >
                  {isUploading('site_favicon') ? 'Uploading...' : 'Upload New Favicon'}
                </label>
              </div>

              <div>
                <button
                  type="button"
                  onClick={() => openMediaLibrary('favicon')}
                  className="inline-flex items-center px-4 py-2 border border-blue-300 rounded-md shadow-sm text-sm font-medium text-blue-700 bg-blue-50 hover:bg-blue-100"
                >
                  Choose from Media Library
                </button>
              </div>
            </div>

            <div className="mt-3 text-xs text-gray-500">
              <p>Recommended: ICO, PNG, or SVG format, 32x32px</p>
              <p>Maximum file size: 1MB</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  )

  const renderGeneralSettings = () => (
    <div className="space-y-6">
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">System Name</label>
        <input
          type="text"
          value={settings.systemName}
          onChange={(e) => handleSettingChange('systemName', e.target.value)}
          className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
        />
      </div>
      
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">Timezone</label>
          <select
            value={settings.timezone}
            onChange={(e) => handleSettingChange('timezone', e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
          >
            <option value="Asia/Manila">Asia/Manila (UTC+8)</option>
            <option value="UTC">UTC (UTC+0)</option>
            <option value="America/New_York">America/New_York (UTC-5)</option>
          </select>
        </div>
        
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">Language</label>
          <select
            value={settings.language}
            onChange={(e) => handleSettingChange('language', e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
          >
            <option value="English">English</option>
            <option value="Filipino">Filipino</option>
            <option value="Cebuano">Cebuano</option>
          </select>
        </div>
      </div>
      
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">Date Format</label>
        <select
          value={settings.dateFormat}
          onChange={(e) => handleSettingChange('dateFormat', e.target.value)}
          className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
        >
          <option value="MM/DD/YYYY">MM/DD/YYYY</option>
          <option value="DD/MM/YYYY">DD/MM/YYYY</option>
          <option value="YYYY-MM-DD">YYYY-MM-DD</option>
        </select>
      </div>
    </div>
  )

  const renderProfileSettings = () => (
    <div className="space-y-6">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">First Name</label>
          <input
            type="text"
            value={settings.firstName}
            onChange={(e) => handleSettingChange('firstName', e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
          />
        </div>
        
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">Last Name</label>
          <input
            type="text"
            value={settings.lastName}
            onChange={(e) => handleSettingChange('lastName', e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
          />
        </div>
      </div>
      
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">Email Address</label>
        <input
          type="email"
          value={settings.email}
          onChange={(e) => handleSettingChange('email', e.target.value)}
          className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
        />
      </div>
      
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">Phone Number</label>
        <input
          type="tel"
          value={settings.phone}
          onChange={(e) => handleSettingChange('phone', e.target.value)}
          className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
        />
      </div>
    </div>
  )

  const renderNotificationSettings = () => (
    <div className="space-y-6">
      {/* Email Service Status */}
      <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
        <div className="flex items-center space-x-2 mb-2">
          <div className="h-2 w-2 bg-green-500 rounded-full animate-pulse"></div>
          <h4 className="text-sm font-medium text-blue-900">Email Service Status</h4>
        </div>
        <p className="text-sm text-blue-700 mb-3">
          Resend email service is configured and ready to send notifications.
        </p>
        <div className="flex space-x-3">
          <a
            href="/admin/email-test"
            className="inline-flex items-center px-3 py-1.5 bg-blue-600 text-white text-xs font-medium rounded-md hover:bg-blue-700 transition-colors"
          >
            Test Email Service
          </a>
          <span className="inline-flex items-center px-3 py-1.5 bg-green-100 text-green-800 text-xs font-medium rounded-md">
            ✓ Configured
          </span>
        </div>
      </div>

      <div className="space-y-4">
        <div className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
          <div>
            <h4 className="text-sm font-medium text-gray-900">Email Notifications</h4>
            <p className="text-sm text-gray-600">Receive notifications via email using Resend service</p>
          </div>
          <button
            onClick={() => handleSettingChange('emailNotifications', !settings.emailNotifications)}
            className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${
              settings.emailNotifications ? 'bg-blue-600' : 'bg-gray-200'
            }`}
          >
            <span
              className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                settings.emailNotifications ? 'translate-x-6' : 'translate-x-1'
              }`}
            />
          </button>
        </div>
        
        <div className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
          <div>
            <h4 className="text-sm font-medium text-gray-900">SMS Notifications</h4>
            <p className="text-sm text-gray-600">Receive notifications via SMS</p>
          </div>
          <button
            onClick={() => handleSettingChange('smsNotifications', !settings.smsNotifications)}
            className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${
              settings.smsNotifications ? 'bg-blue-600' : 'bg-gray-200'
            }`}
          >
            <span
              className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                settings.smsNotifications ? 'translate-x-6' : 'translate-x-1'
              }`}
            />
          </button>
        </div>
        
        <div className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
          <div>
            <h4 className="text-sm font-medium text-gray-900">Push Notifications</h4>
            <p className="text-sm text-gray-600">Receive browser push notifications</p>
          </div>
          <button
            onClick={() => handleSettingChange('pushNotifications', !settings.pushNotifications)}
            className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${
              settings.pushNotifications ? 'bg-blue-600' : 'bg-gray-200'
            }`}
          >
            <span
              className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                settings.pushNotifications ? 'translate-x-6' : 'translate-x-1'
              }`}
            />
          </button>
        </div>
        
        <div className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
          <div>
            <h4 className="text-sm font-medium text-gray-900">Weekly Reports</h4>
            <p className="text-sm text-gray-600">Receive weekly summary reports</p>
          </div>
          <button
            onClick={() => handleSettingChange('weeklyReports', !settings.weeklyReports)}
            className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${
              settings.weeklyReports ? 'bg-blue-600' : 'bg-gray-200'
            }`}
          >
            <span
              className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                settings.weeklyReports ? 'translate-x-6' : 'translate-x-1'
              }`}
            />
          </button>
        </div>
      </div>
    </div>
  )

  const renderSecuritySettings = () => (
    <div className="space-y-6">
      <div className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
        <div>
          <h4 className="text-sm font-medium text-gray-900">Two-Factor Authentication</h4>
          <p className="text-sm text-gray-600">Add an extra layer of security to your account</p>
        </div>
        <button
          onClick={() => handleSettingChange('twoFactorAuth', !settings.twoFactorAuth)}
          className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${
            settings.twoFactorAuth ? 'bg-blue-600' : 'bg-gray-200'
          }`}
        >
          <span
            className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
              settings.twoFactorAuth ? 'translate-x-6' : 'translate-x-1'
            }`}
          />
        </button>
      </div>
      
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">Session Timeout (minutes)</label>
          <select
            value={settings.sessionTimeout}
            onChange={(e) => handleSettingChange('sessionTimeout', e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
          >
            <option value="15">15 minutes</option>
            <option value="30">30 minutes</option>
            <option value="60">1 hour</option>
            <option value="120">2 hours</option>
          </select>
        </div>
        
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">Password Expiry (days)</label>
          <select
            value={settings.passwordExpiry}
            onChange={(e) => handleSettingChange('passwordExpiry', e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
          >
            <option value="30">30 days</option>
            <option value="60">60 days</option>
            <option value="90">90 days</option>
            <option value="never">Never</option>
          </select>
        </div>
      </div>
      
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">Change Password</label>
        <div className="space-y-3">
          <div className="relative">
            <input
              type={showPassword ? 'text' : 'password'}
              placeholder="Current password"
              className="w-full px-3 py-2 pr-10 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            />
            <button
              type="button"
              onClick={() => setShowPassword(!showPassword)}
              className="absolute inset-y-0 right-0 pr-3 flex items-center"
            >
              {showPassword ? <EyeOff className="h-4 w-4 text-gray-400" /> : <Eye className="h-4 w-4 text-gray-400" />}
            </button>
          </div>
          <input
            type="password"
            placeholder="New password"
            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
          />
          <input
            type="password"
            placeholder="Confirm new password"
            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
          />
        </div>
      </div>
    </div>
  )

  const renderSystemSettings = () => (
    <div className="space-y-6">
      <div className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
        <div>
          <h4 className="text-sm font-medium text-gray-900">Auto Backup</h4>
          <p className="text-sm text-gray-600">Automatically backup system data</p>
        </div>
        <button
          onClick={() => handleSettingChange('autoBackup', !settings.autoBackup)}
          className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${
            settings.autoBackup ? 'bg-blue-600' : 'bg-gray-200'
          }`}
        >
          <span
            className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
              settings.autoBackup ? 'translate-x-6' : 'translate-x-1'
            }`}
          />
        </button>
      </div>
      
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">Backup Frequency</label>
        <select
          value={settings.backupFrequency}
          onChange={(e) => handleSettingChange('backupFrequency', e.target.value)}
          className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
          disabled={!settings.autoBackup}
        >
          <option value="hourly">Hourly</option>
          <option value="daily">Daily</option>
          <option value="weekly">Weekly</option>
          <option value="monthly">Monthly</option>
        </select>
      </div>
      
      <div className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
        <div>
          <h4 className="text-sm font-medium text-gray-900">Maintenance Mode</h4>
          <p className="text-sm text-gray-600">Put system in maintenance mode</p>
        </div>
        <button
          onClick={() => handleSettingChange('maintenanceMode', !settings.maintenanceMode)}
          className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${
            settings.maintenanceMode ? 'bg-red-600' : 'bg-gray-200'
          }`}
        >
          <span
            className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
              settings.maintenanceMode ? 'translate-x-6' : 'translate-x-1'
            }`}
          />
        </button>
      </div>
      
      <div className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
        <div>
          <h4 className="text-sm font-medium text-gray-900">Debug Mode</h4>
          <p className="text-sm text-gray-600">Enable debug logging and error details</p>
        </div>
        <button
          onClick={() => handleSettingChange('debugMode', !settings.debugMode)}
          className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${
            settings.debugMode ? 'bg-yellow-600' : 'bg-gray-200'
          }`}
        >
          <span
            className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
              settings.debugMode ? 'translate-x-6' : 'translate-x-1'
            }`}
          />
        </button>
      </div>
    </div>
  )

  const renderContent = () => {
    switch (activeSection) {
      case 'site':
        return renderSiteSettings()
      case 'general':
        return renderGeneralSettings()
      case 'profile':
        return renderProfileSettings()
      case 'notifications':
        return renderNotificationSettings()
      case 'security':
        return renderSecuritySettings()
      case 'system':
        return renderSystemSettings()
      default:
        return renderSiteSettings()
    }
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="bg-gradient-to-r from-gray-600 to-slate-600 rounded-2xl shadow-lg p-8 text-white">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold mb-2">System Settings</h1>
            <p className="text-gray-100 text-lg">Configure system preferences and options</p>
          </div>
          <div className="hidden lg:block">
            <div className="h-20 w-20 bg-white/20 rounded-2xl flex items-center justify-center">
              <SettingsIcon className="h-10 w-10 text-white" />
            </div>
          </div>
        </div>
      </div>

      {/* Settings Layout */}
      <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
        {/* Sidebar */}
        <div className="bg-white rounded-2xl shadow-lg border border-gray-100 p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Settings</h3>
          <nav className="space-y-2">
            {settingSections.map((section) => (
              <button
                key={section.id}
                onClick={() => setActiveSection(section.id)}
                className={`w-full flex items-center space-x-3 px-3 py-2 rounded-lg text-left transition-colors ${
                  activeSection === section.id
                    ? 'bg-blue-100 text-blue-700'
                    : 'text-gray-600 hover:bg-gray-100'
                }`}
              >
                {section.icon}
                <div>
                  <div className="text-sm font-medium">{section.title}</div>
                  <div className="text-xs opacity-75">{section.description}</div>
                </div>
              </button>
            ))}
          </nav>
        </div>

        {/* Content */}
        <div className="lg:col-span-3 bg-white rounded-2xl shadow-lg border border-gray-100 p-6">
          <div className="flex items-center justify-between mb-6">
            <h3 className="text-lg font-semibold text-gray-900">
              {settingSections.find(s => s.id === activeSection)?.title}
            </h3>
            <div className="flex items-center space-x-2">
              <button className="inline-flex items-center px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors">
                <RefreshCw className="h-4 w-4 mr-2" />
                Reset
              </button>
              <button className="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
                <Save className="h-4 w-4 mr-2" />
                Save Changes
              </button>
            </div>
          </div>
          
          {renderContent()}
        </div>
      </div>
    </div>
  )
}
