/**
 * Next.js Middleware for Supabase Authentication
 * 
 * This middleware handles:
 * - Session refresh for authenticated users
 * - Cookie management for Supabase Auth
 * - Automatic token refresh to prevent "refresh_token_not_found" errors
 * - Proper session handling across server and client components
 * 
 * @see https://supabase.com/docs/guides/auth/server-side/nextjs
 */

import { createServerClient } from '@supabase/ssr'
import { NextResponse, type NextRequest } from 'next/server'

export async function middleware(request: NextRequest) {
  let supabaseResponse = NextResponse.next({
    request,
  })

  try {
    const supabase = createServerClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
      {
        cookies: {
          getAll() {
            return request.cookies.getAll()
          },
          setAll(cookiesToSet) {
            cookiesToSet.forEach(({ name, value, options }) => {
              request.cookies.set(name, value)
              supabaseResponse.cookies.set(name, value, options)
            })
          },
        },
      }
    )

    // IMPORTANT: Avoid writing any logic between createServerClient and
    // supabase.auth.getUser(). A simple mistake could make it very hard to debug
    // issues with users being randomly logged out.

    const {
      data: { user },
    } = await supabase.auth.getUser()

    // Optional: Add route protection logic here
    // For now, we'll just handle session refresh without blocking any routes
    
    // If user is authenticated, ensure session is properly refreshed
    if (user) {
      // The session refresh happens automatically through the getUser() call above
      console.log(`[Middleware] Session refreshed for user: ${user.email}`)
    }

    return supabaseResponse
  } catch (error) {
    // If there's an error with the Supabase client, just continue
    console.error('[Middleware] Supabase error:', error)
    return supabaseResponse
  }
}

export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     * - public folder files
     */
    '/((?!_next/static|_next/image|favicon.ico|.*\\.(?:svg|png|jpg|jpeg|gif|webp)$).*)',
  ],
}
