/**
 * Server-Side Site Settings Provider
 * 
 * Enterprise-level solution that pre-loads site settings on the server
 * and injects them into the client-side Redux store to eliminate FOUC.
 * 
 * This is the Facebook/YouTube approach:
 * 1. Server loads settings from database during SSR
 * 2. Settings are serialized and sent to client
 * 3. <PERSON><PERSON> immediately has access to settings without API calls
 * 4. Background images display instantly without delays
 */

import { loadServerSiteSettings } from '@/lib/serverSiteSettings'

import ClientSiteSettingsInitializer from './ClientSiteSettingsInitializer'

interface ServerSiteSettingsProviderProps {
  children: React.ReactNode
}

/**
 * Server component that loads site settings and passes them to client
 */
export default async function ServerSiteSettingsProvider({ 
  children 
}: ServerSiteSettingsProviderProps) {
  // Load settings on the server side
  const serverSettings = await loadServerSiteSettings()
  
  console.log('[ServerSiteSettingsProvider] 🚀 Server-side settings loaded')
  
  return (
    <>
      {/* Initialize client-side Redux store with server settings */}
      <ClientSiteSettingsInitializer settings={serverSettings} />
      {children}
    </>
  )
}
