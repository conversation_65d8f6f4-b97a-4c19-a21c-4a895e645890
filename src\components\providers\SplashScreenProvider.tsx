/**
 * Splash Screen Provider
 * 
 * Manages the global splash screen state and provides context
 * for controlling when the splash screen should be displayed.
 */

'use client'

import { createContext, useContext, useState, useEffect, ReactNode } from 'react'

interface SplashScreenContextType {
  showSplash: boolean
  hideSplash: () => void
  isFirstLoad: boolean
}

const SplashScreenContext = createContext<SplashScreenContextType | undefined>(undefined)

interface SplashScreenProviderProps {
  children: ReactNode
}

export function SplashScreenProvider({ children }: SplashScreenProviderProps) {
  const [showSplash, setShowSplash] = useState(true)
  const [isFirstLoad, setIsFirstLoad] = useState(true)

  useEffect(() => {
    // Check if this is a full page reload or initial load
    const checkLoadType = () => {
      try {
        // Modern way to detect navigation type
        const navigationEntries = performance.getEntriesByType('navigation') as PerformanceNavigationTiming[]
        if (navigationEntries.length > 0) {
          const navigationType = navigationEntries[0].type
          
          // Show splash for reload, navigate, or initial load
          const shouldShowSplash = navigationType === 'reload' || 
                                 navigationType === 'navigate' ||
                                 !sessionStorage.getItem('app-loaded')
          
          if (!shouldShowSplash) {
            setShowSplash(false)
            setIsFirstLoad(false)
          }
        } else {
          // Fallback for older browsers
          const isPageReload = performance.navigation?.type === 1
          if (!isPageReload && sessionStorage.getItem('app-loaded')) {
            setShowSplash(false)
            setIsFirstLoad(false)
          }
        }
      } catch (error) {
        console.warn('[SplashScreen] Could not determine navigation type:', error)
        // Default to showing splash screen on error
      }
    }

    checkLoadType()
  }, [])

  const hideSplash = () => {
    setShowSplash(false)
    setIsFirstLoad(false)
    
    // Mark app as loaded in session storage
    try {
      sessionStorage.setItem('app-loaded', 'true')
    } catch (error) {
      console.warn('[SplashScreen] Could not set session storage:', error)
    }
  }

  const contextValue: SplashScreenContextType = {
    showSplash: showSplash && isFirstLoad,
    hideSplash,
    isFirstLoad
  }

  return (
    <SplashScreenContext.Provider value={contextValue}>
      {children}
    </SplashScreenContext.Provider>
  )
}

/**
 * Hook to use splash screen context
 */
export function useSplashScreenContext() {
  const context = useContext(SplashScreenContext)
  if (context === undefined) {
    throw new Error('useSplashScreenContext must be used within a SplashScreenProvider')
  }
  return context
}

/**
 * Higher-order component to wrap components that need splash screen functionality
 */
export function withSplashScreen<P extends object>(
  Component: React.ComponentType<P>
) {
  return function SplashScreenWrappedComponent(props: P) {
    return (
      <SplashScreenProvider>
        <Component {...props} />
      </SplashScreenProvider>
    )
  }
}
