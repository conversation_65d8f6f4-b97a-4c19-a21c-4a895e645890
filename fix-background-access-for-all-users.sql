-- =====================================================
-- FIX BACKGROUND IMAGE ACCESS FOR ALL USERS
-- =====================================================
-- This script fixes the issue where background images only work for admin users
-- by updating the Row Level Security (RLS) policies to allow PUBLIC access

-- PROBLEM: The current RLS policy only allows authenticated users to read site settings
-- SOLUTION: Allow ALL users (including guests) to read site settings

-- Drop the restrictive policy that blocks guest users
DROP POLICY IF EXISTS "Allow authenticated users to read site settings" ON site_settings;

-- Create a public read policy that allows EVERYONE to read site settings
-- This is safe because site settings are public information (logos, backgrounds, etc.)
DROP POLICY IF EXISTS "Allow public read access to site settings" ON site_settings;
CREATE POLICY "Allow public read access to site settings" ON site_settings
    FOR SELECT USING (is_active = true);

-- Keep write permissions restricted to authenticated users only (security)
DROP POLICY IF EXISTS "Allow authenticated users to update site settings" ON site_settings;
CREATE POLICY "Allow authenticated users to update site settings" ON site_settings
    FOR UPDATE USING (auth.role() = 'authenticated');

DROP POLICY IF EXISTS "Allow authenticated users to insert site settings" ON site_settings;
CREATE POLICY "Allow authenticated users to insert site settings" ON site_settings
    FOR INSERT WITH CHECK (auth.role() = 'authenticated');

-- Ensure service role still has full access (for admin operations)
DROP POLICY IF EXISTS "Allow service role full access to site settings" ON site_settings;
CREATE POLICY "Allow service role full access to site settings" ON site_settings
    FOR ALL USING (auth.role() = 'service_role');

-- Verify the policies were created correctly
SELECT 
    schemaname,
    tablename,
    policyname,
    permissive,
    roles,
    cmd,
    qual
FROM pg_policies 
WHERE tablename = 'site_settings'
ORDER BY policyname;

-- Test query to verify public access works
-- This should return results even without authentication
SELECT 
    setting_key,
    setting_value,
    cloudinary_url,
    setting_type,
    description
FROM site_settings 
WHERE is_active = true 
ORDER BY setting_key;
