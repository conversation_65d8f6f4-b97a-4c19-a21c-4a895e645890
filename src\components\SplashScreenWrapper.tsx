/**
 * Splash Screen Wrapper
 * 
 * Wraps the main application content and conditionally displays
 * the splash screen based on navigation type and loading state.
 */

'use client'

import { ReactNode } from 'react'
import SplashScreen from './SplashScreen'
import { useSplashScreenContext } from './providers/SplashScreenProvider'

interface SplashScreenWrapperProps {
  children: ReactNode
}

export default function SplashScreenWrapper({ children }: SplashScreenWrapperProps) {
  const { showSplash, hideSplash } = useSplashScreenContext()

  return (
    <>
      {/* Show splash screen when needed */}
      {showSplash && (
        <SplashScreen 
          onComplete={hideSplash}
          minDisplayTime={1500} // Minimum 1.5 seconds for professional feel
        />
      )}
      
      {/* Main application content */}
      <div className={showSplash ? 'opacity-0 pointer-events-none' : 'opacity-100'}>
        {children}
      </div>
    </>
  )
}
