import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@/utils/supabase/server'
import { uploadToCloudinary } from '@/lib/cloudinary'
import { SupabaseMediaService } from '@/lib/supabaseMediaService'

export async function POST(request: NextRequest) {
  try {
    console.log('[Site Settings Upload API] Starting upload process')

    // Get the form data
    const formData = await request.formData()
    const file = formData.get('file') as File
    const key = formData.get('key') as string || formData.get('setting_key') as string
    const type = formData.get('type') as string

    if (!file || !key || !type) {
      console.error('[Site Settings Upload API] Missing required fields:', { file: !!file, key, type })
      return NextResponse.json(
        {
          success: false,
          error: 'Missing required fields: file, key, and type are required'
        },
        { status: 400 }
      )
    }

    console.log('[Site Settings Upload API] Upload request:', { key, type, fileName: file.name, fileSize: file.size })

    // Upload directly to Cloudinary (avoid internal API call)
    console.log('[Site Settings Upload API] Uploading to Cloudinary...')

    let cloudinaryResult
    try {
      cloudinaryResult = await uploadToCloudinary(file, {
        folder: 'lgu-uploads/site-assets',
        tags: ['lgu-project', 'site-assets', type],
        resource_type: 'auto'
      })

      console.log('[Site Settings Upload API] Cloudinary upload successful:', {
        url: cloudinaryResult.secure_url,
        publicId: cloudinaryResult.public_id
      })
    } catch (cloudinaryError) {
      console.error('[Site Settings Upload API] Cloudinary upload failed:', cloudinaryError)
      return NextResponse.json(
        {
          success: false,
          error: `Cloudinary upload failed: ${cloudinaryError instanceof Error ? cloudinaryError.message : 'Unknown error'}`
        },
        { status: 500 }
      )
    }

    // Save to database using the update_site_setting function
    const supabase = await createClient()

    // Get current user for audit trail
    const { data: { user }, error: authError } = await supabase.auth.getUser()
    if (authError || !user) {
      console.error('[Site Settings Upload API] Authentication error:', authError)
      return NextResponse.json(
        {
          success: false,
          error: 'Authentication required'
        },
        { status: 401 }
      )
    }

    console.log('[Site Settings Upload API] Saving to database:', {
      key,
      value: cloudinaryResult.secure_url,
      cloudinary_url: cloudinaryResult.secure_url,
      cloudinary_public_id: cloudinaryResult.public_id,
      user_id: user.id
    })

    // Use the update_site_setting function
    const { data, error } = await supabase.rpc('update_site_setting', {
      key_name: key,
      new_value: cloudinaryResult.secure_url,
      new_cloudinary_url: cloudinaryResult.secure_url,
      new_cloudinary_public_id: cloudinaryResult.public_id,
      user_id: user.id
    })

    if (error) {
      console.error('[Site Settings Upload API] Database error:', error)
      
      // Handle missing function/table gracefully
      if (error.code === '42883' || error.code === '42P01' || error.message?.includes('does not exist')) {
        console.warn('[Site Settings Upload API] Database function/table does not exist, but upload was successful')
        return NextResponse.json({
          success: true,
          data: {
            key,
            value: cloudinaryResult.secure_url,
            cloudinary_url: cloudinaryResult.secure_url,
            cloudinary_public_id: cloudinaryResult.public_id
          },
          warning: 'File uploaded successfully but could not save to database (table does not exist)'
        })
      }

      return NextResponse.json(
        { 
          success: false, 
          error: `Database error: ${error.message}`,
          code: error.code
        },
        { status: 500 }
      )
    }

    if (!data) {
      console.error('[Site Settings Upload API] Function returned no data')
      return NextResponse.json(
        {
          success: false,
          error: 'Database function failed to execute'
        },
        { status: 500 }
      )
    }

    console.log('[Site Settings Upload API] Database update successful, now saving to media_assets...')

    // Also save to media_assets table so it appears in media library
    try {
      const mediaAsset = {
        cloudinary_public_id: cloudinaryResult.public_id,
        cloudinary_version: cloudinaryResult.version,
        cloudinary_signature: cloudinaryResult.signature,
        cloudinary_etag: cloudinaryResult.etag,
        original_filename: file.name,
        display_name: `${key} - ${file.name}`,
        file_size: cloudinaryResult.bytes,
        mime_type: file.type,
        format: cloudinaryResult.format,
        width: cloudinaryResult.width,
        height: cloudinaryResult.height,
        folder: cloudinaryResult.folder || 'lgu-uploads/site-assets',
        tags: cloudinaryResult.tags || ['lgu-project', 'site-assets', type],
        description: `Site setting: ${key}`,
        secure_url: cloudinaryResult.secure_url,
        url: cloudinaryResult.url,
        resource_type: (cloudinaryResult.resource_type as 'image' | 'video' | 'raw') || 'image',
        access_mode: 'public' as const,
        uploaded_by: user.id,
        sync_status: 'synced' as const
      }

      const savedAsset = await SupabaseMediaService.upsertMediaAsset(mediaAsset)

      // Track media usage for site settings
      if (savedAsset?.id) {
        try {
          const { error: usageError } = await supabase
            .from('media_usage')
            .upsert({
              media_asset_id: savedAsset.id,
              usage_type: 'site_setting',
              reference_table: 'site_settings',
              reference_id: key,
              usage_context: {
                setting_key: key,
                setting_type: type,
                uploaded_at: new Date().toISOString()
              }
            }, {
              onConflict: 'media_asset_id,usage_type,reference_table,reference_id'
            })

          if (usageError) {
            console.warn('[Site Settings Upload API] Failed to track media usage:', usageError)
          }
        } catch (usageTrackingError) {
          console.warn('[Site Settings Upload API] Media usage tracking failed:', usageTrackingError)
        }
      }

      // Log the sync operation
      await SupabaseMediaService.logSyncOperation({
        operation: 'upload',
        status: 'synced',
        cloudinary_public_id: cloudinaryResult.public_id,
        source: 'admin',
        file_size: cloudinaryResult.bytes,
        operation_data: {
          upload_result: cloudinaryResult,
          setting_key: key,
          setting_type: type
        }
      })

      console.log('[Site Settings Upload API] Successfully saved to media_assets table and tracked usage')
    } catch (mediaError) {
      console.warn('[Site Settings Upload API] Failed to save to media_assets (non-critical):', mediaError)
      // Don't fail the entire operation if media_assets save fails
    }

    console.log('[Site Settings Upload API] Upload completed successfully:', data)

    return NextResponse.json({
      success: true,
      cloudinary_url: cloudinaryResult.secure_url,
      cloudinary_public_id: cloudinaryResult.public_id,
      data: {
        setting_key: key,
        setting_value: cloudinaryResult.secure_url,
        cloudinary_url: cloudinaryResult.secure_url,
        cloudinary_public_id: cloudinaryResult.public_id
      }
    })

  } catch (error) {
    console.error('[Site Settings Upload API] Unexpected error:', error)
    return NextResponse.json(
      { 
        success: false, 
        error: error instanceof Error ? error.message : 'Internal server error' 
      },
      { status: 500 }
    )
  }
}
